<?php

namespace App\Livewire\Admin;

use App\Models\Receipt;
use App\Models\Transaction;
use Filament\Actions\Action;
use Filament\Actions\Concerns\InteractsWithActions;
use Filament\Actions\Contracts\HasActions;
use Filament\Forms;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Livewire\Component;

class TransactionReceiptsTable extends Component implements HasForms, HasActions
{
    use InteractsWithForms;
    use InteractsWithActions;

    public Transaction $transaction;

    public function getActions(): array
    {
        return [
            $this->generateReceiptAction(),
            $this->generateExampleAction(),
        ];
    }

    public function mount(Transaction $transaction): void
    {
        $this->transaction = $transaction;
    }

    public function render()
    {
        return view('livewire.admin.transaction-receipts-table');
    }
    public function generateExampleAction(): Action
    {
        return Action::make('generate-receipt')
            ->label('EXAMPLE')
            ->modalHeading('Generate Receipt')
            ->modalSubmitActionLabel('Generate')

            ->action(function (array $data) {
                // For demo: just dump the email
                \Filament\Notifications\Notification::make()
                    ->title('Receipt generation initiated')
                    ->body("Email: {$data['email']}")
                    ->success()
                    ->send();
            });
    }
    public function generateReceiptAction(): Action
    {


        return Action::make('generate-receipt')
            ->label(__('View Receipt'))
            ->modal()
            ->modalHeading(__('Generate Receipt'))
            ->modalDescription(__('Please review and customize the receipt details before generating the document.'))
            ->modalSubmitActionLabel(__('Generate Receipt'))
            ->mountUsing(function (Form $form, array $arguments) {
                // Logging arguments

                // Find receipt by UUID
                $receipt = Receipt::where('uuid', $arguments['receipt_uuid'])->firstOrFail();

                // Get user data from the transaction's subscription
                $user = $receipt->transaction->subscription->user;

                $form->fill([
                    'email' => $user->email,
                    'displayed_name' => $user->name,
                    'phone' => $user->phone_number,
                    'address_line_1' => $user->address_line_1 ?? '',
                    'address_line_2' => $user->address_line_2 ?? '',
                    'city' => $user->city ?? '',
                    'postal_code' => $user->postal_code ?? '',
                ]);
            })
            ->form([
                Forms\Components\Section::make(__('Contact Information'))
                    ->schema([
                        Forms\Components\TextInput::make('email')
                            ->label(__('Email'))
                            ->email()
                            ->required(),

                        Forms\Components\TextInput::make('displayed_name')
                            ->label(__('Displayed Name'))
                            ->required(),

                        Forms\Components\TextInput::make('phone')
                            ->label(__('Phone'))
                            ->tel(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make(__('Address Information'))
                    ->schema([
                        Forms\Components\TextInput::make('address_line_1')
                            ->label(__('Address Line 1'))
                            ->required(),

                        Forms\Components\TextInput::make('address_line_2')
                            ->label(__('Address Line 2')),

                        Forms\Components\TextInput::make('city')
                            ->label(__('City'))
                            ->required(),

                        Forms\Components\TextInput::make('postal_code')
                            ->label(__('Postal Code'))
                            ->required(),
                    ])
                    ->columns(2),
            ])
            ->action(function (array $data, array $arguments) {
                // Find receipt by UUID
                $receipt = Receipt::where('uuid', $arguments['receipt_uuid'])->firstOrFail();

                // TODO: Implement receipt generation logic here
                // This will be implemented later with the actual receipt generation service

                // For now, just show a success notification
                \Filament\Notifications\Notification::make()
                    ->title(__('Receipt generation initiated'))
                    ->body(__('The receipt will be generated and sent to :email', ['email' => $data['email']]))
                    ->success()
                    ->send();
            });
    }
}
